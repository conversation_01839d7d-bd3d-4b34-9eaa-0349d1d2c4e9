USE PrometheusDB;
GO


PRINT 'Dropping existing tables...'

IF OBJECT_ID('dbo.Grades', 'U') IS NOT NULL
    DROP TABLE dbo.Grades;

IF OBJECT_ID('dbo.Submissions', 'U') IS NOT NULL
    DROP TABLE dbo.Submissions;

IF OBJECT_ID('dbo.ParentStudentLinks', 'U') IS NOT NULL
    DROP TABLE dbo.ParentStudentLinks;

IF OBJECT_ID('dbo.ForumPosts', 'U') IS NOT NULL
    DROP TABLE dbo.ForumPosts;

IF OBJECT_ID('dbo.ForumThreads', 'U') IS NOT NULL
    DROP TABLE dbo.ForumThreads;

IF OBJECT_ID('dbo.CourseContent', 'U') IS NOT NULL
    DROP TABLE dbo.CourseContent;

IF OBJECT_ID('dbo.CourseModules', 'U') IS NOT NULL
    DROP TABLE dbo.CourseModules;
    
IF OBJECT_ID('dbo.Enrollments', 'U') IS NOT NULL
    DROP TABLE dbo.Enrollments;
    
IF OBJECT_ID('dbo.Evaluations', 'U') IS NOT NULL
    DROP TABLE dbo.Evaluations;
    
IF OBJECT_ID('dbo.Courses', 'U') IS NOT NULL
    DROP TABLE dbo.Courses;

IF OBJECT_ID('dbo.Users', 'U') IS NOT NULL
    DROP TABLE dbo.Users;

IF OBJECT_ID('dbo.Roles', 'U') IS NOT NULL
    DROP TABLE dbo.Roles;

IF OBJECT_ID('dbo.SystemConfiguration', 'U') IS NOT NULL
    DROP TABLE dbo.SystemConfiguration;

IF OBJECT_ID('dbo.AuditLogs', 'U') IS NOT NULL
    DROP TABLE dbo.AuditLogs;
GO

PRINT 'Creating tables...'

CREATE TABLE dbo.Roles (
    RoleID INT IDENTITY(1,1) PRIMARY KEY,
    RoleName NVARCHAR(50) NOT NULL UNIQUE
);
GO

CREATE TABLE dbo.Users (
    UserID INT IDENTITY(1,1) PRIMARY KEY,
    RoleID INT NOT NULL,
    FirstName NVARCHAR(100) NOT NULL,
    LastName NVARCHAR(100) NOT NULL,
    Email NVARCHAR(255) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    ProfilePicturePath NVARCHAR(512) NULL,
    IsActive BIT NOT NULL DEFAULT 1, -- 1 = Active, 0 = Inactive
    CreationDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_Users_Roles FOREIGN KEY (RoleID) REFERENCES dbo.Roles(RoleID)
);
GO

CREATE TABLE dbo.Courses (
    CourseID INT IDENTITY(1,1) PRIMARY KEY,
    TeacherUserID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    CourseCode NVARCHAR(20) NOT NULL UNIQUE,
    Description NVARCHAR(MAX) NULL,
    IsArchived BIT NOT NULL DEFAULT 0, -- 0 = Active, 1 = Archived
    CreationDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_Courses_Users_Teacher FOREIGN KEY (TeacherUserID) REFERENCES dbo.Users(UserID)
);
GO

CREATE TABLE dbo.Enrollments (
    EnrollmentID INT IDENTITY(1,1) PRIMARY KEY,
    StudentID INT NOT NULL,
    CourseID INT NOT NULL,
    EnrollmentDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_Enrollments_Users_Student FOREIGN KEY (StudentID) REFERENCES dbo.Users(UserID),
    CONSTRAINT FK_Enrollments_Courses FOREIGN KEY (CourseID) REFERENCES dbo.Courses(CourseID) ON DELETE CASCADE,
    CONSTRAINT UQ_Enrollments_StudentCourse UNIQUE (StudentID, CourseID)
);
GO

CREATE TABLE dbo.CourseModules (
    ModuleID INT IDENTITY(1,1) PRIMARY KEY,
    CourseID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    Description NVARCHAR(MAX) NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsPublished BIT NOT NULL DEFAULT 0,
    CONSTRAINT FK_CourseModules_Courses FOREIGN KEY (CourseID) REFERENCES dbo.Courses(CourseID) ON DELETE CASCADE
);
GO

CREATE TABLE dbo.CourseContent (
    ContentID INT IDENTITY(1,1) PRIMARY KEY,
    ModuleID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    ContentType NVARCHAR(10) NOT NULL,
    ContentPath NVARCHAR(512) NOT NULL,
    SortOrder INT NOT NULL DEFAULT 0,
    IsVisible BIT NOT NULL DEFAULT 1,
    CONSTRAINT FK_CourseContent_CourseModules FOREIGN KEY (ModuleID) REFERENCES dbo.CourseModules(ModuleID) ON DELETE CASCADE
);
GO

CREATE TABLE dbo.Evaluations (
    EvaluationID INT IDENTITY(1,1) PRIMARY KEY,
    CourseID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    Instructions NVARCHAR(MAX) NULL,
    PointsPossible DECIMAL(9, 2) NOT NULL,
    DueDate DATETIME NOT NULL,
    IsPublished BIT NOT NULL DEFAULT 0,
    CONSTRAINT FK_Evaluations_Courses FOREIGN KEY (CourseID) REFERENCES dbo.Courses(CourseID) ON DELETE CASCADE
);
GO

CREATE TABLE dbo.Submissions (
    SubmissionID INT IDENTITY(1,1) PRIMARY KEY,
    EvaluationID INT NOT NULL,
    StudentID INT NOT NULL,
    SubmissionTimestamp DATETIME NOT NULL DEFAULT GETDATE(),
    FilePath NVARCHAR(512) NOT NULL,
    IsLate BIT NOT NULL DEFAULT 0,
    CONSTRAINT FK_Submissions_Evaluations FOREIGN KEY (EvaluationID) REFERENCES dbo.Evaluations(EvaluationID),
    CONSTRAINT FK_Submissions_Users_Student FOREIGN KEY (StudentID) REFERENCES dbo.Users(UserID),
    CONSTRAINT UQ_Submissions_StudentEvaluation UNIQUE (StudentID, EvaluationID)
);
GO

CREATE TABLE dbo.Grades (
    GradeID INT IDENTITY(1,1) PRIMARY KEY,
    EvaluationID INT NOT NULL,
    StudentID INT NOT NULL,
    GraderID INT NOT NULL,
    Score DECIMAL(9, 2) NOT NULL,
    FeedbackComments NVARCHAR(MAX) NULL,
    DateGraded DATETIME NOT NULL DEFAULT GETDATE(),
    IsPublishedToStudent BIT NOT NULL DEFAULT 0,
    CONSTRAINT FK_Grades_Evaluations FOREIGN KEY (EvaluationID) REFERENCES dbo.Evaluations(EvaluationID),
    CONSTRAINT FK_Grades_Users_Student FOREIGN KEY (StudentID) REFERENCES dbo.Users(UserID),
    CONSTRAINT FK_Grades_Users_Grader FOREIGN KEY (GraderID) REFERENCES dbo.Users(UserID),
    CONSTRAINT UQ_Grades_StudentEvaluation UNIQUE (StudentID, EvaluationID)
);
GO

CREATE TABLE dbo.ParentStudentLinks (
    ParentUserID INT NOT NULL,
    StudentID INT NOT NULL,
    CONSTRAINT PK_ParentStudentLinks PRIMARY KEY (ParentUserID, StudentID),
    CONSTRAINT FK_ParentStudentLinks_Users_Parent FOREIGN KEY (ParentUserID) REFERENCES dbo.Users(UserID),
    CONSTRAINT FK_ParentStudentLinks_Users_Student FOREIGN KEY (StudentID) REFERENCES dbo.Users(UserID) ON DELETE CASCADE
);
GO

CREATE TABLE dbo.ForumThreads (
    ThreadID INT IDENTITY(1,1) PRIMARY KEY,
    CourseID INT NOT NULL,
    AuthorID INT NOT NULL,
    Title NVARCHAR(255) NOT NULL,
    IsPinned BIT NOT NULL DEFAULT 0,
    CreationDate DATETIME NOT NULL DEFAULT GETDATE(),
    CONSTRAINT FK_ForumThreads_Courses FOREIGN KEY (CourseID) REFERENCES dbo.Courses(CourseID) ON DELETE CASCADE,
    CONSTRAINT FK_ForumThreads_Users_Author FOREIGN KEY (AuthorID) REFERENCES dbo.Users(UserID)
);
GO

CREATE TABLE dbo.ForumPosts (
    PostID INT IDENTITY(1,1) PRIMARY KEY,
    ThreadID INT NOT NULL,
    AuthorID INT NOT NULL,
    MessageBody NVARCHAR(MAX) NOT NULL,
    CreationDate DATETIME NOT NULL DEFAULT GETDATE(),
    ParentPostID INT NULL,
    CONSTRAINT FK_ForumPosts_ForumThreads FOREIGN KEY (ThreadID) REFERENCES dbo.ForumThreads(ThreadID) ON DELETE CASCADE,
    CONSTRAINT FK_ForumPosts_Users_Author FOREIGN KEY (AuthorID) REFERENCES dbo.Users(UserID),
    CONSTRAINT FK_ForumPosts_ParentPost FOREIGN KEY (ParentPostID) REFERENCES dbo.ForumPosts(PostID)
);
GO

CREATE TABLE dbo.SystemConfiguration(
	ConfigKey NVARCHAR(100) PRIMARY KEY,
	ConfigValue NVARCHAR(MAX) NOT NULL
);
GO

CREATE TABLE dbo.AuditLogs(
    LogID INT IDENTITY(1,1) PRIMARY KEY,
    LogTimestamp DATETIME NOT NULL DEFAULT GETDATE(),
    AdminUserID INT NOT NULL,
    ActionDescription NVARCHAR(1000) NOT NULL,
    AffectedEntity NVARCHAR(50) NULL,
    AffectedEntityID INT NULL,
    CONSTRAINT FK_AuditLogs_Users_Admin FOREIGN KEY (AdminUserID) REFERENCES dbo.Users(UserID)
);
GO

PRINT 'Seeding initial data...'

INSERT INTO dbo.Roles (RoleName) VALUES ('Administrator');
INSERT INTO dbo.Roles (RoleName) VALUES ('Teacher');
INSERT INTO dbo.Roles (RoleName) VALUES ('Student');
INSERT INTO dbo.Roles (RoleName) VALUES ('Parent');
GO

DECLARE @AdminRoleID INT;
SELECT @AdminRoleID = RoleID FROM dbo.Roles WHERE RoleName = 'Administrator';

IF NOT EXISTS (SELECT 1 FROM dbo.Users WHERE Email = '<EMAIL>')
BEGIN
    INSERT INTO dbo.Users (RoleID, FirstName, LastName, Email, PasswordHash, IsActive)
    VALUES (
        @AdminRoleID,
        'Admin',
        'User',
        '<EMAIL>',
        '',
        1
    );
END
GO

PRINT 'Database schema creation and initial seeding complete.'
GO