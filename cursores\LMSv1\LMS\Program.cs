using System;
using System.Collections.Generic;
using System.Text;
using System.Net;
using System.IO;
using System.Data.SqlClient;
using System.Security.Cryptography;

namespace LMS
{
    public class UserPoco
    {
        public int UserID { get; set; }
        public string FirstName { get; set; }
        public string LastName { get; set; }
        public string Email { get; set; }
        public string RoleName { get; set; }
        public int RoleID { get; set; }
        public bool IsActive { get; set; }
    }

    public class CoursePoco
    {
        public int CourseID { get; set; }
        public string Title { get; set; }
        public string CourseCode { get; set; }
        public string Description { get; set; }
        public string TeacherName { get; set; }
        public int TeacherUserID { get; set; }
        public bool IsArchived { get; set; }
    }

    public class CourseModulePoco
    {
        public int ModuleID { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public bool IsPublished { get; set; }
        public int SortOrder { get; set; }
        public List<CourseContentPoco> Contents { get; set; }
        public CourseModulePoco() { Contents = new List<CourseContentPoco>(); }
    }

    public class CourseContentPoco
    {
        public int ContentID { get; set; }
        public string Title { get; set; }
        public string ContentType { get; set; }
        public string ContentPath { get; set; }
        public bool IsVisible { get; set; }
    }

    public class EvaluationPoco
    {
        public int EvaluationID { get; set; }
        public string Title { get; set; }
        public string Instructions { get; set; }
        public decimal PointsPossible { get; set; }
        public DateTime DueDate { get; set; }
        public bool IsPublished { get; set; }
    }

    internal class Program
    {
        private static readonly Dictionary<string, UserPoco> _sessions = new Dictionary<string, UserPoco>();
        private static readonly string _dbConnectionString = "Server=(localdb)\\mssqllocaldb;Database=PrometheusDB;Trusted_Connection=True;";

        static void Main()
        {
            HttpListener listener = new HttpListener();
            listener.Prefixes.Add("http://localhost:8080/");
            listener.Start();
            Console.WriteLine("Listening on port 8080...");
            while (true)
            {
                HttpListenerContext context = listener.GetContext();
                HandleRequest(context);
            }
        }

        private static string HashPassword(string password)
        {
            byte[] salt = new byte[16];
            new RNGCryptoServiceProvider().GetBytes(salt);
            Rfc2898DeriveBytes pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
            byte[] hash = pbkdf2.GetBytes(20);
            byte[] hashBytes = new byte[36];
            Array.Copy(salt, 0, hashBytes, 0, 16);
            Array.Copy(hash, 0, hashBytes, 16, 20);
            return Convert.ToBase64String(hashBytes);
        }

        private static bool VerifyPassword(string savedPasswordHash, string password)
        {
            byte[] hashBytes = Convert.FromBase64String(savedPasswordHash);
            byte[] salt = new byte[16];
            Array.Copy(hashBytes, 0, salt, 0, 16);
            Rfc2898DeriveBytes pbkdf2 = new Rfc2898DeriveBytes(password, salt, 10000);
            byte[] hash = pbkdf2.GetBytes(20);
            for (int i = 0; i < 20; i++) if (hashBytes[i + 16] != hash[i]) return false;
            return true;
        }

        private static UserPoco GetAuthenticatedUser(HttpListenerContext context)
        {
            Cookie sessionCookie = context.Request.Cookies["session_id"];
            if (sessionCookie != null && _sessions.ContainsKey(sessionCookie.Value) && sessionCookie.Value.Length > 10)
            {
                return _sessions[sessionCookie.Value];
            }
            return null;
        }

        private static string RenderPage(string title, string body, UserPoco user, string message, string messageType)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("<!DOCTYPE html><html><head><title>Prometheus LMS - ");
            sb.Append(title);
            sb.Append("</title><style>");
            sb.Append("body{font-family:Verdana,sans-serif;background-color:#f4f4f4;margin:0;padding:0;} .container{width:80%;margin:auto;overflow:hidden;padding:20px;} header{background:#333;color:#fff;padding:10px 20px;min-height:70px;border-bottom:#77aaff 3px solid;}");
            sb.Append("header h1{margin:0;float:left;} header nav{float:right;margin-top:25px;} header a{color:#fff;text-decoration:none;font-size:16px;padding:5px 15px;} header a:hover{background:#555;}");
            sb.Append("table{width:100%;border-collapse:collapse;margin-top:20px;} th,td{padding:12px;border:1px solid #ddd;text-align:left;} th{background-color:#77aaff;color:white;} tr:nth-child(even){background-color:#f2f2f2;}");
            sb.Append("form{margin-top:20px;padding:20px;background:#fff;border:1px solid #ddd;border-radius:5px;} form label{display:block;margin-bottom:5px;font-weight:bold;} form input[type='text'],form input[type='password'],form input[type='email'],form input[type='number'], form input[type='datetime-local'], form select,textarea{width:100%;padding:8px;margin-bottom:10px;box-sizing:border-box;border:1px solid #ccc;border-radius:4px;}");
            sb.Append("input[type='submit'], .button{background:#333;color:#fff;padding:10px 15px;border:none;cursor:pointer;text-decoration:none;display:inline-block;border-radius:4px;} .button-danger{background:#d9534f;} .button-create{background:#5cb85c;}");
            sb.Append(".card{background:#fff;padding:20px;margin-bottom:20px;border:1px solid #ddd;border-radius:5px;} .card h3{margin-top:0;}");
            sb.Append(".error{padding:15px;color:#a94442;background-color:#f2dede;border:1px solid #ebccd1;border-radius:4px;margin-bottom:20px;} .success{padding:15px;color:#3c763d;background-color:#dff0d8;border:1px solid #d6e9c6;border-radius:4px;margin-bottom:20px;}");
            sb.Append(".flex-container {display: flex; justify-content: space-between; align-items: center;}");
            sb.Append(".tabs{border-bottom:1px solid #ddd;margin-bottom:20px;} .tab{display:inline-block;padding:10px 20px;background:#f8f8f8;border:1px solid #ddd;border-bottom:none;cursor:pointer;margin-right:5px;} .tab.active{background:#fff;border-bottom:1px solid #fff;} .tab-content{display:none;} .tab-content.active{display:block;}");
            sb.Append("</style></head><body>");
            sb.Append("<header><div class='container'><div class='flex-container'><h1>Prometheus LMS</h1><nav>");
            if (user != null && user.UserID > 0 && user.RoleName != null && user.RoleName.Length > 1)
            {
                if (user.RoleName == "Administrator")
                {
                    sb.Append("<a href='/admin/dashboard'>Dashboard</a><a href='/admin/users'>Users</a><a href='/admin/courses'>Courses</a><a href='/admin/settings'>Settings</a>");
                }
                if (user.RoleName == "Teacher")
                {
                    sb.Append("<a href='/teacher/dashboard'>My Courses</a>");
                }
                if (user.RoleName == "Student")
                {
                    sb.Append("<a href='/student/dashboard'>My Courses</a>");
                }
                sb.Append("<a href='/profile'>Profile</a><a href='/logout'>Logout (" + user.FirstName + ")</a>");
            }
            else
            {
                sb.Append("<a href='/login'>Login</a>");
            }
            sb.Append("</nav></div></div></header>");
            sb.Append("<div class='container'>");
            sb.Append("<h2>" + title + "</h2>");
            if (!string.IsNullOrEmpty(message))
            {
                sb.Append("<div class='" + (messageType == "error" ? "error" : "success") + "'>" + message + "</div>");
            }
            sb.Append(body);
            sb.Append("</div></body></html>");
            return sb.ToString();
        }

        private static void HandleRequest(HttpListenerContext context)
        {
            HttpListenerRequest request = context.Request;
            HttpListenerResponse response = context.Response;
            string url = request.Url.AbsolutePath;
            string method = request.HttpMethod;
            UserPoco user = GetAuthenticatedUser(context);
            string responseString = "";
            bool isRedirect = false;
            string message = request.QueryString["msg"];
            string messageType = request.QueryString["type"];

            System.Collections.Specialized.NameValueCollection formData = new System.Collections.Specialized.NameValueCollection();

            if (url == "\"/favicon.ico\"") goto exit1;

            if (method == "POST")
            {
                string postData;
                using (StreamReader reader = new StreamReader(request.InputStream, request.ContentEncoding))
                {
                    postData = reader.ReadToEnd();
                }
                if (!string.IsNullOrEmpty(postData))
                {
                    string[] pairs = postData.Split('&');
                    foreach (string pair in pairs)
                    {
                        string[] keyValue = pair.Split('=');
                        if (keyValue.Length == 2) formData.Add(Uri.UnescapeDataString(keyValue[0]), Uri.UnescapeDataString(keyValue[1].Replace("+", " ")));
                    }
                }
            }

            if (url == "/login")
            {
                if (method == "GET")
                {
                    responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, message, messageType);
                }
                else
                {
                    string email = formData["email"];
                    string password = formData["password"];
                    if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password) || password.Length < 3 || !email.Contains("@"))
                    {
                        responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, "Invalid input format.", "error");
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            string sql = "SELECT u.UserID, u.FirstName, u.LastName, u.Email, u.PasswordHash, u.IsActive, r.RoleName, r.RoleID FROM Users u JOIN Roles r ON u.RoleID = r.RoleID WHERE u.Email = @Email";
                            using (SqlCommand cmd = new SqlCommand(sql, conn))
                            {
                                cmd.Parameters.AddWithValue("@Email", email);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read())
                                    {
                                        if (Convert.ToBoolean(reader["IsActive"]) && reader["PasswordHash"] != DBNull.Value && VerifyPassword(reader["PasswordHash"].ToString(), password))
                                        {
                                            UserPoco authUser = new UserPoco { UserID = (int)reader["UserID"], FirstName = reader["FirstName"].ToString(), LastName = reader["LastName"].ToString(), Email = reader["Email"].ToString(), RoleName = reader["RoleName"].ToString(), RoleID = (int)reader["RoleID"] };
                                            string sessionId = Guid.NewGuid().ToString();
                                            _sessions[sessionId] = authUser;
                                            Cookie sessionCookie = new Cookie("session_id", sessionId);
                                            sessionCookie.Path = "/";
                                            response.Cookies.Add(sessionCookie);
                                            isRedirect = true;
                                            response.RedirectLocation = (authUser.RoleName == "Administrator") ? "/admin/dashboard" : (authUser.RoleName == "Teacher") ? "/teacher/dashboard" : "/student/dashboard";
                                        }
                                    }
                                }
                            }
                        }
                        if (!isRedirect)
                        {
                            responseString = RenderPage("Login", "<form method='post' action='/login'><label for='email'>Email:</label><input type='email' id='email' name='email' required><label for='password'>Password:</label><input type='password' id='password' name='password' required><input type='submit' value='Login'></form>", null, "Invalid username or password.", "error");
                        }
                    }
                }
            }
            else if (url == "/logout")
            {
                Cookie sessionCookie = request.Cookies["session_id"];
                if (sessionCookie != null && _sessions.ContainsKey(sessionCookie.Value)) _sessions.Remove(sessionCookie.Value);
                response.Cookies.Add(new Cookie("session_id", "") { Expires = DateTime.Now.AddDays(-1), Path = "/" });
                isRedirect = true;
                response.RedirectLocation = "/login?msg=Successfully+logged+out.&type=success";
            }
            else if (user == null && url != "/login")
            {
                isRedirect = true;
                response.RedirectLocation = "/login?msg=You+must+be+logged+in+to+view+this+page.&type=error";
            }
            else
            {
                if (url.StartsWith("/admin") && user.RoleName != "Administrator")
                {
                    responseString = RenderPage("Access Denied", "You do not have permission to view this page.", user, null, null);
                    response.StatusCode = 403;
                }
                else if (url == "/admin/dashboard")
                {
                    StringBuilder body = new StringBuilder();
                    int userCount = 0; int courseCount = 0;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Users", conn)) userCount = (int)cmd.ExecuteScalar();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses", conn)) courseCount = (int)cmd.ExecuteScalar();
                    }
                    body.Append("<div class='card'><h3>System Statistics</h3><p>Total Users: " + userCount + "</p><p>Total Courses: " + courseCount + "</p></div>");
                    responseString = RenderPage("Admin Dashboard", body.ToString(), user, message, messageType);
                }
                else if (url == "/admin/users")
                {
                    StringBuilder body = new StringBuilder();
                    body.Append("<a href='/admin/users/create' class='button button-create'>Create New User</a>");
                    body.Append("<table><tr><th>Name</th><th>Email</th><th>Role</th><th>Status</th><th>Action</th></tr>");
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT u.UserID, u.FirstName, u.LastName, u.Email, r.RoleName, u.IsActive FROM Users u JOIN Roles r ON u.RoleID = r.RoleID ORDER BY u.LastName, u.FirstName", conn))
                        {
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    body.Append("<tr><td>" + reader["FirstName"] + " " + reader["LastName"] + "</td><td>" + reader["Email"] + "</td><td>" + reader["RoleName"] + "</td><td>" + (Convert.ToBoolean(reader["IsActive"]) ? "Active" : "Inactive") + "</td><td><a href='/admin/users/edit?id=" + reader["UserID"] + "' class='button'>Edit</a></td></tr>");
                                }
                            }
                        }
                    }
                    body.Append("</table>");
                    responseString = RenderPage("User Management", body.ToString(), user, message, messageType);
                }
                else if (url == "/admin/users/create")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<form method='post' action='/admin/users/create'>");
                        body.Append("<label for='firstName'>First Name:</label><input type='text' id='firstName' name='firstName' required>");
                        body.Append("<label for='lastName'>Last Name:</label><input type='text' id='lastName' name='lastName' required>");
                        body.Append("<label for='email'>Email:</label><input type='email' id='email' name='email' required>");
                        body.Append("<label for='password'>Initial Password:</label><input type='password' id='password' name='password' required>");
                        body.Append("<label for='roleId'>Role:</label><select id='roleId' name='roleId'>");
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read()) body.Append("<option value='" + reader["RoleID"] + "'>" + reader["RoleName"] + "</option>");
                                }
                            }
                        }
                        body.Append("</select><input type='submit' value='Create User'></form>");
                        responseString = RenderPage("Create New User", body.ToString(), user, null, null);
                    }
                    else
                    {
                        string passwordHash = HashPassword(formData["password"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("INSERT INTO Users (FirstName, LastName, Email, PasswordHash, RoleID, IsActive) VALUES (@FirstName, @LastName, @Email, @PasswordHash, @RoleID, 1)", conn))
                            {
                                cmd.Parameters.AddWithValue("@FirstName", formData["firstName"]);
                                cmd.Parameters.AddWithValue("@LastName", formData["lastName"]);
                                cmd.Parameters.AddWithValue("@Email", formData["email"]);
                                cmd.Parameters.AddWithValue("@PasswordHash", passwordHash);
                                cmd.Parameters.AddWithValue("@RoleID", int.Parse(formData["roleId"]));
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/admin/users?msg=User+created+successfully.&type=success";
                    }
                }
                else if (url.StartsWith("/admin/users/edit"))
                {
                    int targetId = int.Parse(request.QueryString["id"]);
                    if (method == "GET")
                    {
                        UserPoco targetUser = null;
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT UserID, FirstName, LastName, Email, IsActive, RoleID FROM Users WHERE UserID = @UserID", conn))
                            {
                                cmd.Parameters.AddWithValue("@UserID", targetId);
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    if (reader.Read()) targetUser = new UserPoco { UserID = (int)reader["UserID"], FirstName = reader["FirstName"].ToString(), LastName = reader["LastName"].ToString(), Email = reader["Email"].ToString(), IsActive = (bool)reader["IsActive"], RoleID = (int)reader["RoleID"] };
                                }
                            }
                        }
                        StringBuilder body = new StringBuilder();
                        body.Append("<form method='post' action='/admin/users/edit?id=" + targetId + "'>");
                        body.Append("<label>First Name:</label><input type='text' name='firstName' value='" + targetUser.FirstName + "'>");
                        body.Append("<label>Last Name:</label><input type='text' name='lastName' value='" + targetUser.LastName + "'>");
                        body.Append("<label>Email:</label><input type='email' name='email' value='" + targetUser.Email + "'>");
                        body.Append("<label>Role:</label><select name='roleId'>");
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT RoleID, RoleName FROM Roles", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read()) body.Append("<option value='" + reader["RoleID"] + "'" + ((int)reader["RoleID"] == targetUser.RoleID ? " selected" : "") + ">" + reader["RoleName"] + "</option>");
                                }
                            }
                        }
                        body.Append("</select><label><input type='checkbox' name='isActive' value='true' " + (targetUser.IsActive ? "checked" : "") + "> Is Active</label><input type='submit' value='Save Changes'></form>");
                        responseString = RenderPage("Edit User", body.ToString(), user, null, null);
                    }
                    else
                    {
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("UPDATE Users SET FirstName = @FirstName, LastName = @LastName, Email = @Email, RoleID = @RoleID, IsActive = @IsActive WHERE UserID = @UserID", conn))
                            {
                                cmd.Parameters.AddWithValue("@FirstName", formData["firstName"]);
                                cmd.Parameters.AddWithValue("@LastName", formData["lastName"]);
                                cmd.Parameters.AddWithValue("@Email", formData["email"]);
                                cmd.Parameters.AddWithValue("@RoleID", int.Parse(formData["roleId"]));
                                cmd.Parameters.AddWithValue("@IsActive", formData["isActive"] == "true");
                                cmd.Parameters.AddWithValue("@UserID", targetId);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/admin/users?msg=User+updated+successfully.&type=success";
                    }
                }
                else if (url == "/admin/settings")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='tabs'>");
                        body.Append("<div class='tab active' onclick='showTab(\"general\")'>General</div>");
                        body.Append("<div class='tab' onclick='showTab(\"security\")'>Security</div>");
                        body.Append("<div class='tab' onclick='showTab(\"email\")'>Email</div>");
                        body.Append("</div>");

                        Dictionary<string, string> settings = new Dictionary<string, string>();
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT ConfigKey, ConfigValue FROM SystemConfiguration", conn))
                            {
                                using (SqlDataReader reader = cmd.ExecuteReader())
                                {
                                    while (reader.Read())
                                    {
                                        settings[reader["ConfigKey"].ToString()] = reader["ConfigValue"].ToString();
                                    }
                                }
                            }
                        }

                        body.Append("<form method='post' action='/admin/settings'>");

                        body.Append("<div id='general' class='tab-content active'>");
                        body.Append("<h3>General Settings</h3>");
                        body.Append("<label for='systemName'>System Name:</label>");
                        body.Append("<input type='text' id='systemName' name='systemName' value='" + (settings.ContainsKey("SystemName") ? settings["SystemName"] : "Prometheus LMS") + "'>");
                        body.Append("<label for='systemDescription'>System Description:</label>");
                        body.Append("<textarea id='systemDescription' name='systemDescription' rows='3'>" + (settings.ContainsKey("SystemDescription") ? settings["SystemDescription"] : "") + "</textarea>");
                        body.Append("<label for='maintenanceMode'>Maintenance Mode:</label>");
                        body.Append("<select id='maintenanceMode' name='maintenanceMode'>");
                        body.Append("<option value='false'" + (settings.ContainsKey("MaintenanceMode") && settings["MaintenanceMode"] == "false" ? " selected" : "") + ">Disabled</option>");
                        body.Append("<option value='true'" + (settings.ContainsKey("MaintenanceMode") && settings["MaintenanceMode"] == "true" ? " selected" : "") + ">Enabled</option>");
                        body.Append("</select>");
                        body.Append("</div>");

                        body.Append("<div id='security' class='tab-content'>");
                        body.Append("<h3>Security Settings</h3>");
                        body.Append("<label for='sessionTimeout'>Session Timeout (minutes):</label>");
                        body.Append("<input type='number' id='sessionTimeout' name='sessionTimeout' value='" + (settings.ContainsKey("SessionTimeout") ? settings["SessionTimeout"] : "30") + "' min='5' max='480'>");
                        body.Append("<label for='passwordMinLength'>Minimum Password Length:</label>");
                        body.Append("<input type='number' id='passwordMinLength' name='passwordMinLength' value='" + (settings.ContainsKey("PasswordMinLength") ? settings["PasswordMinLength"] : "8") + "' min='6' max='50'>");
                        body.Append("<label for='maxLoginAttempts'>Maximum Login Attempts:</label>");
                        body.Append("<input type='number' id='maxLoginAttempts' name='maxLoginAttempts' value='" + (settings.ContainsKey("MaxLoginAttempts") ? settings["MaxLoginAttempts"] : "5") + "' min='3' max='20'>");
                        body.Append("</div>");

                        body.Append("<div id='email' class='tab-content'>");
                        body.Append("<h3>Email Settings</h3>");
                        body.Append("<label for='smtpServer'>SMTP Server:</label>");
                        body.Append("<input type='text' id='smtpServer' name='smtpServer' value='" + (settings.ContainsKey("SmtpServer") ? settings["SmtpServer"] : "") + "'>");
                        body.Append("<label for='smtpPort'>SMTP Port:</label>");
                        body.Append("<input type='number' id='smtpPort' name='smtpPort' value='" + (settings.ContainsKey("SmtpPort") ? settings["SmtpPort"] : "587") + "'>");
                        body.Append("<label for='smtpUsername'>SMTP Username:</label>");
                        body.Append("<input type='text' id='smtpUsername' name='smtpUsername' value='" + (settings.ContainsKey("SmtpUsername") ? settings["SmtpUsername"] : "") + "'>");
                        body.Append("<label for='smtpPassword'>SMTP Password:</label>");
                        body.Append("<input type='password' id='smtpPassword' name='smtpPassword' value='" + (settings.ContainsKey("SmtpPassword") ? settings["SmtpPassword"] : "") + "'>");
                        body.Append("<label for='fromEmail'>From Email Address:</label>");
                        body.Append("<input type='email' id='fromEmail' name='fromEmail' value='" + (settings.ContainsKey("FromEmail") ? settings["FromEmail"] : "") + "'>");
                        body.Append("</div>");

                        body.Append("<input type='submit' value='Save Changes'>");
                        body.Append("</form>");

                        body.Append("<script>");
                        body.Append("function showTab(tabName) {");
                        body.Append("var tabs = document.getElementsByClassName('tab');");
                        body.Append("var contents = document.getElementsByClassName('tab-content');");
                        body.Append("for (var i = 0; i < tabs.length; i++) { tabs[i].classList.remove('active'); }");
                        body.Append("for (var i = 0; i < contents.length; i++) { contents[i].classList.remove('active'); }");
                        body.Append("event.target.classList.add('active');");
                        body.Append("document.getElementById(tabName).classList.add('active');");
                        body.Append("}");
                        body.Append("</script>");

                        responseString = RenderPage("System Settings", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string[] configKeys = { "systemName", "systemDescription", "maintenanceMode", "sessionTimeout", "passwordMinLength", "maxLoginAttempts", "smtpServer", "smtpPort", "smtpUsername", "smtpPassword", "fromEmail" };
                        string[] dbKeys = { "SystemName", "SystemDescription", "MaintenanceMode", "SessionTimeout", "PasswordMinLength", "MaxLoginAttempts", "SmtpServer", "SmtpPort", "SmtpUsername", "SmtpPassword", "FromEmail" };

                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            for (int i = 0; i < configKeys.Length; i++)
                            {
                                if (formData[configKeys[i]] != null)
                                {
                                    using (SqlCommand cmd = new SqlCommand("IF EXISTS (SELECT 1 FROM SystemConfiguration WHERE ConfigKey = @Key) UPDATE SystemConfiguration SET ConfigValue = @Value WHERE ConfigKey = @Key ELSE INSERT INTO SystemConfiguration (ConfigKey, ConfigValue) VALUES (@Key, @Value)", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@Key", dbKeys[i]);
                                        cmd.Parameters.AddWithValue("@Value", formData[configKeys[i]]);
                                        cmd.ExecuteNonQuery();
                                    }
                                }
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/admin/settings?msg=Settings+updated+successfully.&type=success";
                    }
                }
                else if (url.StartsWith("/course"))
                {
                    int courseId = int.Parse(request.QueryString["Id"]);
                    bool isUserEnrolled = false;
                    bool isUserTeacher = false;
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Enrollments WHERE CourseID = @CourseID AND StudentID = @UserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if ((int)cmd.ExecuteScalar() > 0) isUserEnrolled = true;
                        }
                        using (SqlCommand cmd = new SqlCommand("SELECT COUNT(*) FROM Courses WHERE CourseID = @CourseID AND TeacherUserID = @UserID", conn))
                        {
                            cmd.Parameters.AddWithValue("@CourseID", courseId);
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            if ((int)cmd.ExecuteScalar() > 0) isUserTeacher = true;
                        }
                    }

                    if ((user.RoleName == "Student" && isUserEnrolled == false) || (user.RoleName == "Teacher" && isUserTeacher == false) && user.RoleName != "Administrator")
                    {
                        responseString = RenderPage("Access Denied", "You are not authorized to view this course.", user, null, null);
                        response.StatusCode = 403;
                    }
                    else if (url == "/course/add-module" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("INSERT INTO CourseModules (CourseID, Title, IsPublished) VALUES (@CourseID, @Title, 0);", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                cmd.Parameters.AddWithValue("@Title", formData["moduleTitle"]);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Module+created.&type=success";
                    }
                    else if (url == "/course/add-content" && method == "POST" && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        int moduleId = int.Parse(request.QueryString["moduleId"]);
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("INSERT INTO CourseContent (ModuleID, Title, ContentType, ContentPath, IsVisible) VALUES (@ModuleID, @Title, @ContentType, @Path, 1)", conn))
                            {
                                cmd.Parameters.AddWithValue("@ModuleID", moduleId);
                                cmd.Parameters.AddWithValue("@Title", formData["contentTitle"]);
                                cmd.Parameters.AddWithValue("@ContentType", formData["contentType"]);
                                cmd.Parameters.AddWithValue("@Path", formData["contentPath"]);
                                cmd.ExecuteNonQuery();
                            }
                        }
                        isRedirect = true;
                        response.RedirectLocation = "/course?id=" + courseId + "&msg=Content+added.&type=success";
                    }
                    else if (url.StartsWith("/course/evaluation/create") && (isUserTeacher || user.RoleName == "Administrator"))
                    {
                        if (method == "GET")
                        {
                            StringBuilder body = new StringBuilder();
                            body.Append("<form method='post' action='/course/evaluation/create?Id=" + courseId + "'>");
                            body.Append("<label>Title:</label><input type='text' name='title' required>");
                            body.Append("<label>Instructions:</label><textarea name='instructions' rows='5'></textarea>");
                            body.Append("<label>Points Possible:</label><input type='number' step='0.01' name='points' required>");
                            body.Append("<label>Due Date:</label><input type='datetime-local' name='dueDate' required>");
                            body.Append("<label><input type='checkbox' name='isPublished' value='true'> Publish Now</label>");
                            body.Append("<input type='submit' value='Create Evaluation'></form>");
                            responseString = RenderPage("Create Evaluation", body.ToString(), user, null, null);
                        }
                        else
                        {
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("INSERT INTO Evaluations (CourseID, Title, Instructions, PointsPossible, DueDate, IsPublished) VALUES (@CourseID, @Title, @Instructions, @Points, @DueDate, @IsPublished)", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    cmd.Parameters.AddWithValue("@Title", formData["title"]);
                                    cmd.Parameters.AddWithValue("@Instructions", formData["instructions"]);
                                    cmd.Parameters.AddWithValue("@Points", decimal.Parse(formData["points"]));
                                    cmd.Parameters.AddWithValue("@DueDate", DateTime.Parse(formData["dueDate"]));
                                    cmd.Parameters.AddWithValue("@IsPublished", formData["isPublished"] == "true");
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/course?id=" + courseId + "&msg=Evaluation+created.&type=success";
                        }
                    }
                    else
                    {
                        CoursePoco currentCourse = null;
                        List<CourseModulePoco> modules = new List<CourseModulePoco>();
                        List<EvaluationPoco> evaluations = new List<EvaluationPoco>();
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT c.CourseID, c.Title, c.CourseCode, u.FirstName + ' ' + u.LastName as TeacherName FROM Courses c JOIN Users u ON c.TeacherUserID = u.UserID WHERE c.CourseID = @CourseID", conn))
                            {
                                cmd.Parameters.AddWithValue("@CourseID", courseId);
                                using (SqlDataReader reader = cmd.ExecuteReader()) { if (reader.Read()) currentCourse = new CoursePoco { CourseID = (int)reader["CourseID"], Title = reader["Title"].ToString(), CourseCode = reader["CourseCode"].ToString(), TeacherName = reader["TeacherName"].ToString() }; }
                            }
                            if (currentCourse != null)
                            {
                                using (SqlCommand cmd = new SqlCommand("SELECT ModuleID, Title, IsPublished, Description FROM CourseModules WHERE CourseID = @CourseID ORDER BY SortOrder", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            bool isPublished = (bool)reader["IsPublished"];
                                            if (isUserTeacher || user.RoleName == "Administrator" || isPublished)
                                            {
                                                modules.Add(new CourseModulePoco { ModuleID = (int)reader["ModuleID"], Title = reader["Title"].ToString(), IsPublished = isPublished, Description = reader["Description"] as string });
                                            }
                                        }
                                    }
                                }

                                foreach (CourseModulePoco module in modules)
                                {
                                    using (SqlCommand cmd = new SqlCommand("SELECT ContentID, Title, ContentType, ContentPath, IsVisible FROM CourseContent WHERE ModuleID = @ModuleID ORDER BY SortOrder", conn))
                                    {
                                        cmd.Parameters.AddWithValue("@ModuleID", module.ModuleID);
                                        using (SqlDataReader reader = cmd.ExecuteReader())
                                        {
                                            while (reader.Read())
                                            {
                                                bool isVisible = (bool)reader["IsVisible"];
                                                if (isUserTeacher || user.RoleName == "Administrator" || isVisible)
                                                {
                                                    module.Contents.Add(new CourseContentPoco { ContentID = (int)reader["ContentID"], Title = reader["Title"].ToString(), ContentType = reader["ContentType"].ToString(), ContentPath = reader["ContentPath"].ToString(), IsVisible = isVisible });
                                                }
                                            }
                                        }
                                    }
                                }
                                using (SqlCommand cmd = new SqlCommand("SELECT EvaluationID, Title, DueDate, IsPublished FROM Evaluations WHERE CourseID = @CourseID ORDER BY DueDate", conn))
                                {
                                    cmd.Parameters.AddWithValue("@CourseID", courseId);
                                    using (SqlDataReader reader = cmd.ExecuteReader())
                                    {
                                        while (reader.Read())
                                        {
                                            bool isPublished = (bool)reader["IsPublished"];
                                            if (isUserTeacher || user.RoleName == "Administrator" || isPublished)
                                            {
                                                evaluations.Add(new EvaluationPoco { EvaluationID = (int)reader["EvaluationID"], Title = reader["Title"].ToString(), DueDate = (DateTime)reader["DueDate"], IsPublished = isPublished });
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        StringBuilder body = new StringBuilder();
                        body.Append("<h3>" + currentCourse.CourseCode + " - Taught by " + currentCourse.TeacherName + "</h3>");
                        if (isUserTeacher || user.RoleName == "Administrator")
                        {
                            body.Append("<a href='/course/roster?id=" + courseId + "' class='button'>View Roster</a> ");
                            body.Append("<a href='/course/evaluation/create?Id=" + courseId + "' class='button button-create'>New Evaluation</a>");
                        }
                        body.Append("<h4>Course Modules</h4>");
                        foreach (CourseModulePoco module in modules)
                        {
                            body.Append("<div class='card'><h3>" + module.Title + (isUserTeacher || user.RoleName == "Administrator" ? (module.IsPublished ? " (Published)" : " (Hidden)") : "") + "</h3>" + (module.Description != null ? "<p>" + module.Description + "</p>" : "") + "<ul>");
                            foreach (CourseContentPoco content in module.Contents)
                            {
                                body.Append("<li><a href='" + content.ContentPath + "'>" + content.Title + " (" + content.ContentType + ")</a>" + ((isUserTeacher || user.RoleName == "Administrator") && !content.IsVisible ? " (Hidden)" : "") + "</li>");
                            }
                            body.Append("</ul>");
                            if (isUserTeacher || user.RoleName == "Administrator")
                            {
                                body.Append("<hr/><form method='post' action='/course/add-content?Id=" + courseId + "&moduleId=" + module.ModuleID + "' style='padding:5px; margin-top:5px; border:none; background:none;'><input type='text' name='contentTitle' placeholder='Content Title' style='width:auto; padding:5px;'><input type='text' name='contentPath' placeholder='Content Path/URL' style='width:auto;padding:5px;'><select name='contentType' style='width:auto;padding:5px;'><option value='File'>File</option><option value='URL'>URL</option></select><input type='submit' value='Add Content' style='padding:5px 8px;'></form>");
                            }
                            body.Append("</div>");
                        }
                        if (isUserTeacher || user.RoleName == "Administrator")
                        {
                            body.Append("<div class='card'><h4>Add New Module</h4><form method='post' action='/course/add-module?id=" + courseId + "'><input type='text' name='moduleTitle' placeholder='Module Title' required><input type='submit' value='Add Module'></form></div>");
                        }
                        body.Append("<h4>Evaluations</h4><table><tr><th>Title</th><th>Due Date</th></tr>");
                        foreach (EvaluationPoco eval in evaluations)
                        {
                            body.Append("<tr><td>" + eval.Title + ((isUserTeacher || user.RoleName == "Administrator") && !eval.IsPublished ? " (Hidden)" : "") + "</td><td>" + eval.DueDate.ToString("yyyy-MM-dd HH:mm") + "</td></tr>");
                        }
                        body.Append("</table>");
                        responseString = RenderPage(currentCourse.Title, body.ToString(), user, message, messageType);
                    }
                }
                else if (url == "/teacher/dashboard" || url == "/student/dashboard")
                {
                    StringBuilder body = new StringBuilder();
                    List<CoursePoco> courses = new List<CoursePoco>();
                    using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                    {
                        conn.Open();
                        string sql;
                        if (user.RoleName == "Teacher")
                        {
                            sql = "SELECT CourseID, CourseCode, Title, IsArchived FROM Courses WHERE TeacherUserID = @UserID ORDER BY Title";
                        }
                        else
                        {
                            sql = "SELECT c.CourseID, c.CourseCode, c.Title, c.IsArchived FROM Courses c JOIN Enrollments e ON c.CourseID = e.CourseID WHERE e.StudentID = @UserID ORDER BY c.Title";
                        }
                        using (SqlCommand cmd = new SqlCommand(sql, conn))
                        {
                            cmd.Parameters.AddWithValue("@UserID", user.UserID);
                            using (SqlDataReader reader = cmd.ExecuteReader())
                            {
                                while (reader.Read()) courses.Add(new CoursePoco { CourseID = (int)reader["CourseID"], CourseCode = reader["CourseCode"].ToString(), Title = reader["Title"].ToString(), IsArchived = (bool)reader["IsArchived"] });
                            }
                        }
                    }
                    body.Append("<h4>Active Courses</h4><table><tr><th>Code</th><th>Title</th><th>Action</th></tr>");
                    foreach (CoursePoco c in courses) if (!c.IsArchived) body.Append("<tr><td>" + c.CourseCode + "</td><td>" + c.Title + "</td><td><a href='/course?id=" + c.CourseID + "' class='button'>" + (user.RoleName == "Teacher" ? "Manage" : "View") + "</a></td></tr>");
                    body.Append("</table><h4>Archived Courses</h4><table><tr><th>Code</th><th>Title</th><th>Action</th></tr>");
                    foreach (CoursePoco c in courses) if (c.IsArchived) body.Append("<tr><td>" + c.CourseCode + "</td><td>" + c.Title + "</td><td><a href='/course?id=" + c.CourseID + "' class='button'>" + (user.RoleName == "Teacher" ? "Manage" : "View") + "</a></td></tr>");
                    body.Append("</table>");
                    responseString = RenderPage("My Courses", body.ToString(), user, message, messageType);
                }
                else if (url == "/profile")
                {
                    if (method == "GET")
                    {
                        StringBuilder body = new StringBuilder();
                        body.Append("<div class='card'><h3>My Details</h3>");
                        body.Append("<p><strong>Name:</strong> " + user.FirstName + " " + user.LastName + "</p>");
                        body.Append("<p><strong>Email:</strong> " + user.Email + "</p>");
                        body.Append("<p><strong>Role:</strong> " + user.RoleName + "</p></div>");
                        body.Append("<div class='card'><h3>Change Password</h3><form method='post' action='/profile'>");
                        body.Append("<label>Current Password:</label><input type='password' name='currentPassword' required>");
                        body.Append("<label>New Password:</label><input type='password' name='newPassword' required>");
                        body.Append("<input type='submit' value='Change Password'></form></div>");
                        responseString = RenderPage("My Profile", body.ToString(), user, message, messageType);
                    }
                    else
                    {
                        string currentPassword = formData["currentPassword"];
                        string newPassword = formData["newPassword"];
                        string savedHash = "";
                        using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                        {
                            conn.Open();
                            using (SqlCommand cmd = new SqlCommand("SELECT PasswordHash FROM Users WHERE UserID = @UserID", conn))
                            {
                                cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                savedHash = cmd.ExecuteScalar().ToString();
                            }
                        }
                        if (VerifyPassword(savedHash, currentPassword))
                        {
                            string newHash = HashPassword(newPassword);
                            using (SqlConnection conn = new SqlConnection(_dbConnectionString))
                            {
                                conn.Open();
                                using (SqlCommand cmd = new SqlCommand("UPDATE Users SET PasswordHash = @NewHash WHERE UserID = @UserID", conn))
                                {
                                    cmd.Parameters.AddWithValue("@NewHash", newHash);
                                    cmd.Parameters.AddWithValue("@UserID", user.UserID);
                                    cmd.ExecuteNonQuery();
                                }
                            }
                            isRedirect = true;
                            response.RedirectLocation = "/profile?msg=Password+updated+successfully.&type=success";
                        }
                        else
                        {
                            isRedirect = true;
                            response.RedirectLocation = "/profile?msg=Incorrect+current+password.&type=error";
                        }
                    }
                }
                else
                {
                    responseString = RenderPage("Not Found", "<h1>404 - Page Not Found</h1><p>The page you are looking for does not exist or you do not have permission to access it.</p>", user, null, null);
                    response.StatusCode = 404;
                }
            }

        exit1:
            if (isRedirect && response != null && response.OutputStream.CanWrite)
            {
                response.StatusCode = 302;
            }
            else if (response != null && response.OutputStream.CanWrite)
            {
                byte[] buffer = Encoding.UTF8.GetBytes(responseString);
                response.ContentLength64 = buffer.Length;
                response.ContentType = "text/html";
                response.ContentEncoding = Encoding.UTF8;
                response.OutputStream.Write(buffer, 0, buffer.Length);
            }
            if (response != null)
            {
                response.OutputStream.Close();
            }
        }
    }
}
