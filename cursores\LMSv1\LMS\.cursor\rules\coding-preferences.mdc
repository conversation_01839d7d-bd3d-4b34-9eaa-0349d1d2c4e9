---
description: 
globs: 
alwaysApply: true
---

# Coding pattern preferences

- Always prefer simple solutions  
– You should be careful to only make changes that are requested or you are confident are well understood and related to the change being requested  
– When fixing an issue or bug, do not introduce a new pattern or technology without first exhausting all options for the existing implementation. And if you finally do this, make sure to remove the old implementation afterwards so we don’t have duplicate logic.  
– Never add stubbing or fake data patterns to code that affects the dev or prod environments  
- Code with no comments or additional documentation.
- Focus on the areas of code relevant to the task
- Do not touch code that is unrelated to the task
- Avoid making major changes to the patterns and architecture of how a feature works, after it has shown to work well, unless explicitly instructed
- Always think about what other methods and areas of code might be affected by code changes
- You are not able to create new methods, all changes goes on the existing
- Code is legacy, follow legacy procedural style.